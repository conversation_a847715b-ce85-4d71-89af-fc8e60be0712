<?php
return [
    'frontend' => [
        // Virtual Page Resolver - Creates virtual page objects BEFORE PageResolver
        'flight-landing-pages/virtual-page-resolver' => [
            'target' => \Bgs\FlightLandingPages\Middleware\VirtualPageResolverMiddleware::class,
            'before' => [
                'typo3/cms-frontend/page-resolver',
            ],
            'after' => [
                'typo3/cms-frontend/site',
            ],
        ],

        // Virtual Page Content - Injects virtual content AFTER TSFE initialization
        'flight-landing-pages/virtual-page-content' => [
            'target' => \Bgs\FlightLandingPages\Middleware\VirtualPageContentMiddleware::class,
            'after' => [
                'typo3/cms-frontend/prepare-tsfe-rendering',
            ],
            'before' => [
                'typo3/cms-frontend/content-length-headers',
            ],
        ],
    ],
];
