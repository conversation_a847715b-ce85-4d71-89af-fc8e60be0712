<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\EventListener;

use Bgs\FlightLandingPages\Service\VirtualRouteService;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Frontend\Event\BeforePageIsResolvedEvent;

/**
 * Virtual Route Detection Listener
 * 
 * Listens to BeforePageIsResolvedEvent to detect virtual routes
 * and store the context for later processing.
 */
class VirtualRouteDetectionListener
{
    public function __construct(
        private readonly VirtualRouteService $virtualRouteService
    ) {}

    public function __invoke(BeforePageIsResolvedEvent $event): void
    {
        error_log("VirtualRouteDetectionListener: *** EVENT LISTENER CALLED! ***");

        $request = $event->getRequest();

        // Check if this is a virtual route request (set by our middleware)
        $virtualRouteMatch = $request->getAttribute('virtual_route');

        if ($virtualRouteMatch) {
            error_log("VirtualRouteDetectionListener: Processing virtual route request");
            error_log("VirtualRouteDetectionListener: Landing page UID: {$virtualRouteMatch['landingPage']['uid']}");
            error_log("VirtualRouteDetectionListener: Template page UID: {$virtualRouteMatch['templatePageUid']}");
            error_log("VirtualRouteDetectionListener: Flight route: {$virtualRouteMatch['flightRoute']['route_slug']}");

            // Store virtual route context for later processing by other event listeners
            $this->virtualRouteService->setVirtualRoute($virtualRouteMatch);
        } else {
            error_log("VirtualRouteDetectionListener: Normal page request, no virtual route context");

            // Clear any previous virtual route state for normal requests
            $this->virtualRouteService->clearVirtualRoute();
        }
    }
}
