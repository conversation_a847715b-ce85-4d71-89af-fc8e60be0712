<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\EventListener;

use Bgs\FlightLandingPages\Service\VirtualRouteService;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Frontend\Event\BeforePageIsResolvedEvent;

/**
 * Virtual Route Detection Listener
 * 
 * Listens to BeforePageIsResolvedEvent to detect virtual routes
 * and store the context for later processing.
 */
class VirtualRouteDetectionListener
{
    public function __construct(
        private readonly VirtualRouteService $virtualRouteService
    ) {}

    public function __invoke(BeforePageIsResolvedEvent $event): void
    {
        // ALWAYS log that the listener is being called - even for normal pages
        error_log("VirtualRouteDetectionListener: *** EVENT LISTENER CALLED! ***");

        $request = $event->getRequest();
        $path = trim($request->getUri()->getPath(), '/');
        error_log("VirtualRouteDetectionListener: Processing ANY path: {$path}");

        $site = $request->getAttribute('site');

        if (!$site instanceof Site) {
            error_log("VirtualRouteDetectionListener: No site found in request");
            return;
        }

        error_log("VirtualRouteDetectionListener: Site found: " . $site->getIdentifier());

        // Clear any previous virtual route state
        $this->virtualRouteService->clearVirtualRoute();

        // Detect virtual route
        $virtualRouteMatch = $this->virtualRouteService->detectVirtualRoute($path, $site);

        if ($virtualRouteMatch) {
            // Store virtual route context for later processing
            $this->virtualRouteService->setVirtualRoute($virtualRouteMatch);

            // Log for debugging
            error_log("VirtualRouteDetectionListener: Virtual route detected for path: {$path}");
            error_log("VirtualRouteDetectionListener: Landing page UID: {$virtualRouteMatch['landingPage']['uid']}");
            error_log("VirtualRouteDetectionListener: Template page UID: {$virtualRouteMatch['templatePageUid']}");
            error_log("VirtualRouteDetectionListener: Flight route: {$virtualRouteMatch['flightRoute']['route_slug']}");
        } else {
            error_log("VirtualRouteDetectionListener: No virtual route match found for path: {$path}");
        }
    }
}
