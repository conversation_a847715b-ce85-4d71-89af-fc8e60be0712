<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\EventListener;

use Bgs\FlightLandingPages\Service\VirtualRouteService;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Frontend\Event\BeforePageIsResolvedEvent;

/**
 * Virtual Route Detection Listener
 * 
 * Listens to BeforePageIsResolvedEvent to detect virtual routes
 * and store the context for later processing.
 */
class VirtualRouteDetectionListener
{
    public function __construct(
        private readonly VirtualRouteService $virtualRouteService
    ) {}

    public function __invoke(BeforePageIsResolvedEvent $event): void
    {
        $request = $event->getRequest();
        $site = $request->getAttribute('site');

        if (!$site instanceof Site) {
            return;
        }

        $path = trim($request->getUri()->getPath(), '/');

        // Clear any previous virtual route state
        $this->virtualRouteService->clearVirtualRoute();

        // Detect virtual route
        $virtualRouteMatch = $this->virtualRouteService->detectVirtualRoute($path, $site);

        if ($virtualRouteMatch) {
            // Store virtual route context for later processing
            $this->virtualRouteService->setVirtualRoute($virtualRouteMatch);
            
            // Log for debugging
            error_log("VirtualRouteDetectionListener: Virtual route detected for path: {$path}");
            error_log("VirtualRouteDetectionListener: Landing page UID: {$virtualRouteMatch['landingPage']['uid']}");
            error_log("VirtualRouteDetectionListener: Template page UID: {$virtualRouteMatch['templatePageUid']}");
            error_log("VirtualRouteDetectionListener: Flight route: {$virtualRouteMatch['flightRoute']['route_slug']}");
        }
    }
}
