<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\EventListener;

use Bgs\FlightLandingPages\Service\VirtualRouteService;
use TYPO3\CMS\Frontend\Event\AfterCacheableContentIsGeneratedEvent;

/**
 * Virtual Content Processing Listener
 * 
 * Listens to AfterCacheableContentIsGeneratedEvent to process placeholders
 * in the generated content for virtual routes.
 */
class VirtualContentProcessingListener
{
    public function __construct(
        private readonly VirtualRouteService $virtualRouteService
    ) {}

    public function __invoke(AfterCacheableContentIsGeneratedEvent $event): void
    {
        // Check if we're processing a virtual route
        if (!$this->virtualRouteService->isVirtualRoute()) {
            return;
        }

        $virtualRouteData = $this->virtualRouteService->getCurrentVirtualRoute();
        if (!$virtualRouteData) {
            return;
        }

        $request = $event->getRequest();
        $response = $event->getResponse();

        // Get the current content
        $content = (string)$response->getBody();

        // Process placeholders in the content
        $processedContent = $this->virtualRouteService->processPlaceholdersInContent(
            $content,
            $virtualRouteData['flightRoute']
        );

        // Only update if content changed
        if ($processedContent !== $content) {
            // Create new response with processed content
            $newResponse = $response->withBody(
                \TYPO3\CMS\Core\Http\Stream::create($processedContent)
            );
            
            $event->setResponse($newResponse);
            
            error_log("VirtualContentProcessingListener: Placeholders processed in content");
        }
    }
}
