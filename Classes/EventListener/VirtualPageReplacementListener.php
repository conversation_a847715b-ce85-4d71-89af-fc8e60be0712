<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\EventListener;

use Bgs\FlightLandingPages\Service\VirtualRouteService;
use TYPO3\CMS\Frontend\Event\AfterPageWithRootLineIsResolvedEvent;

/**
 * Virtual Page Replacement Listener
 * 
 * Listens to AfterPageWithRootLineIsResolvedEvent to replace the resolved page
 * with virtual page data when processing virtual routes.
 */
class VirtualPageReplacementListener
{
    public function __construct(
        private readonly VirtualRouteService $virtualRouteService
    ) {}

    public function __invoke(AfterPageWithRootLineIsResolvedEvent $event): void
    {
        // Check if we're processing a virtual route
        if (!$this->virtualRouteService->isVirtualRoute()) {
            return;
        }

        $virtualRouteData = $this->virtualRouteService->getCurrentVirtualRoute();
        if (!$virtualRouteData) {
            return;
        }

        // Load template page
        $templatePage = $this->virtualRouteService->loadTemplatePage($virtualRouteData['templatePageUid']);
        if (!$templatePage) {
            error_log("VirtualPageReplacementListener: Template page not found: {$virtualRouteData['templatePageUid']}");
            return;
        }

        // Create virtual page data
        $virtualPageData = $this->virtualRouteService->createVirtualPageData(
            $virtualRouteData['landingPage'],
            $templatePage,
            $virtualRouteData['flightRoute'],
            $virtualRouteData['originalPath']
        );

        // Get the current page and rootline from the event
        $currentPage = $event->getPage();
        $currentRootLine = $event->getRootLine();

        // Replace the page data while keeping the structure
        $modifiedPage = array_merge($currentPage, $virtualPageData);

        // Update the rootline to include our virtual page
        $modifiedRootLine = $currentRootLine;
        if (!empty($modifiedRootLine)) {
            // Replace the last entry (current page) with our virtual page
            $modifiedRootLine[array_key_last($modifiedRootLine)] = $modifiedPage;
        }

        // Set the modified page and rootline
        $event->setPage($modifiedPage);
        $event->setRootLine($modifiedRootLine);

        error_log("VirtualPageReplacementListener: Page replaced with virtual page data");
        error_log("VirtualPageReplacementListener: Virtual page title: {$modifiedPage['title']}");
        error_log("VirtualPageReplacementListener: Virtual page slug: {$modifiedPage['slug']}");
    }
}
