<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\EventListener;

use Bgs\FlightLandingPages\Service\VirtualRouteService;
use TYPO3\CMS\Frontend\Event\AfterPageWithRootLineIsResolvedEvent;

/**
 * Virtual Page Replacement Listener
 * 
 * Listens to AfterPageWithRootLineIsResolvedEvent to replace the resolved page
 * with virtual page data when processing virtual routes.
 */
class VirtualPageReplacementListener
{
    public function __construct(
        private readonly VirtualRouteService $virtualRouteService
    ) {}

    public function __invoke(AfterPageWithRootLineIsResolvedEvent $event): void
    {
        error_log("VirtualPageReplacementListener: *** EVENT LISTENER CALLED! ***");

        // Get virtual route data directly from the request attribute (set by middleware)
        $request = $event->getRequest();
        $virtualRouteMatch = $request->getAttribute('virtual_route');

        if (!$virtualRouteMatch) {
            error_log("VirtualPageReplacementListener: No virtual route context, skipping");
            return;
        }

        error_log("VirtualPageReplacementListener: Processing virtual route - replacing page data");
        error_log("VirtualPageReplacementListener: Virtual route match data: " . print_r($virtualRouteMatch, true));

        if (!isset($virtualRouteMatch['templatePageUid'])) {
            error_log("VirtualPageReplacementListener: templatePageUid not found in virtual route match");
            return;
        }

        error_log("VirtualPageReplacementListener: Template page UID: {$virtualRouteMatch['templatePageUid']}");

        // Get the controller and access page data directly
        $controller = $event->getController();
        $currentPage = $controller->page;
        $currentRootLine = $controller->rootLine;

        error_log("VirtualPageReplacementListener: Current page UID: {$currentPage['uid']}");
        error_log("VirtualPageReplacementListener: Current page title: {$currentPage['title']}");

        // Load template page from database
        $templatePageUid = $virtualRouteMatch['templatePageUid'];
        error_log("VirtualPageReplacementListener: About to load template page: {$templatePageUid}");

        try {
            $templatePage = $this->virtualRouteService->loadTemplatePage($templatePageUid);
            error_log("VirtualPageReplacementListener: Template page loaded successfully");
        } catch (\Exception $e) {
            error_log("VirtualPageReplacementListener: Error loading template page: " . $e->getMessage());
            return;
        }

        if (!$templatePage) {
            error_log("VirtualPageReplacementListener: Template page not found: {$templatePageUid}");
            return;
        }

        error_log("VirtualPageReplacementListener: Template page loaded - UID: {$templatePage['uid']}, Title: {$templatePage['title']}");

        // Replace ONLY the content-related fields from template page
        // Keep all structural and routing properties from the landing page
        $modifiedPage = $currentPage; // Start with current page

        // Only replace content and SEO fields from template page
        $fieldsToReplace = [
            'title',
            'subtitle',
            'abstract',
            'keywords',
            'description',
            'seo_title',
            'og_title',
            'og_description',
            'nav_title',
            // Keep layout settings from template
            'layout',
            'backend_layout',
            'backend_layout_next_level',
        ];

        foreach ($fieldsToReplace as $field) {
            if (isset($templatePage[$field])) {
                $modifiedPage[$field] = $templatePage[$field];
            }
        }

        error_log("VirtualPageReplacementListener: Preserved landing page UID: {$modifiedPage['uid']}, PID: {$modifiedPage['pid']}");
        error_log("VirtualPageReplacementListener: Replaced title with: {$modifiedPage['title']}");

        // Update the rootline to include our modified page
        $modifiedRootLine = $currentRootLine;
        if (!empty($modifiedRootLine)) {
            // Replace the last entry (current page) with our modified page
            $modifiedRootLine[array_key_last($modifiedRootLine)] = $modifiedPage;
        }

        // Set the modified page and rootline directly on the controller
        $controller->page = $modifiedPage;
        $controller->rootLine = $modifiedRootLine;

        error_log("VirtualPageReplacementListener: Page replaced with template page data");
        error_log("VirtualPageReplacementListener: New page title: {$modifiedPage['title']}");
    }
}
