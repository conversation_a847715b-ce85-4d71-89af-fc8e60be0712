<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\Middleware;

use Bgs\FlightLandingPages\Service\VirtualRouteService;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use TYPO3\CMS\Core\Routing\PageArguments;
use TYPO3\CMS\Core\Routing\SiteRouteResult;
use TYPO3\CMS\Core\Site\Entity\Site;

/**
 * Virtual Route Handler Middleware
 * 
 * This middleware runs BEFORE PageResolver to intercept virtual routes
 * and modify the request to point to the landing page instead.
 * This prevents PageResolver from rejecting virtual routes with 404.
 */
class VirtualRouteHandler implements MiddlewareInterface
{
    public function __construct(
        private readonly VirtualRouteService $virtualRouteService
    ) {}

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        error_log("VirtualRouteHandler: *** MIDDLEWARE CALLED ***");

        $site = $request->getAttribute('site');
        if (!$site instanceof Site) {
            error_log("VirtualRouteHandler: No site found, passing through");
            return $handler->handle($request);
        }

        $path = trim($request->getUri()->getPath(), '/');
        $basePath = rtrim($site->getBase()->getPath(), '/');
        error_log("VirtualRouteHandler: Processing path: {$path}");
        error_log("VirtualRouteHandler: Site base path: {$basePath}");

        // Clear any previous virtual route state
        $this->virtualRouteService->clearVirtualRoute();

        // Detect virtual route
        $virtualRouteMatch = $this->virtualRouteService->detectVirtualRoute($path, $site);

        if ($virtualRouteMatch) {
            error_log("VirtualRouteHandler: Virtual route detected! Modifying request...");

            // Store virtual route context for later processing by PSR-14 events
            $this->virtualRouteService->setVirtualRoute($virtualRouteMatch);

            // Modify the request to point to the landing page instead of the virtual route
            // This allows PageResolver to find the landing page successfully
            $landingPagePath = $this->getLandingPagePath($virtualRouteMatch['landingPage'], $site);

            error_log("VirtualRouteHandler: Redirecting to landing page path: {$landingPagePath}");

            // Create a new URI pointing to the landing page
            $newUri = $request->getUri()->withPath($landingPagePath);
            $modifiedRequest = $request->withUri($newUri);

            // Also update the routing attribute to match the new path
            $previousResult = $request->getAttribute('routing');
            if ($previousResult) {
                error_log("VirtualRouteHandler: Updating routing attribute");
                // Create a new SiteRouteResult with the modified path
                $newRouteResult = $previousResult->withTail(ltrim($landingPagePath, '/'));
                $modifiedRequest = $modifiedRequest->withAttribute('routing', $newRouteResult);
            }

            // Add a flag to indicate this is a virtual route request
            $modifiedRequest = $modifiedRequest->withAttribute('virtual_route', $virtualRouteMatch);

            return $handler->handle($modifiedRequest);
        }

        error_log("VirtualRouteHandler: No virtual route match, passing through");
        return $handler->handle($request);
    }

    /**
     * Get the path for the landing page that PageResolver can find
     */
    private function getLandingPagePath(array $landingPage, Site $site): string
    {
        // Use the landing page slug
        $slug = $landingPage['slug'] ?? '';
        $basePath = rtrim($site->getBase()->getPath(), '/');

        error_log("VirtualRouteHandler: Landing page slug: {$slug}");
        error_log("VirtualRouteHandler: Site base path: {$basePath}");

        // Ensure it starts with the site base path
        if (!str_starts_with($slug, $basePath)) {
            $slug = $basePath . '/' . ltrim($slug, '/');
        }

        error_log("VirtualRouteHandler: Final landing page path: {$slug}");
        return $slug;
    }
}
