<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\Middleware;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\Controller\TypoScriptFrontendController;

/**
 * Virtual Page Content Middleware
 * 
 * This middleware runs AFTER TSFE is initialized and injects virtual page
 * content into the TSFE object so that normal TYPO3 rendering can proceed.
 */
class VirtualPageContentMiddleware implements MiddlewareInterface
{
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        // Check if this is a virtual route
        $virtualRouteData = $request->getAttribute('virtualRoute.data');
        
        if (!$virtualRouteData || !($virtualRouteData['isVirtualRoute'] ?? false)) {
            return $handler->handle($request);
        }

        // Inject virtual page data into TSFE
        $this->injectVirtualPageIntoTSFE($request, $virtualRouteData);

        return $handler->handle($request);
    }

    /**
     * Inject virtual page data into TSFE so normal rendering can proceed
     */
    protected function injectVirtualPageIntoTSFE(ServerRequestInterface $request, array $virtualRouteData): void
    {
        $tsfe = $GLOBALS['TSFE'] ?? null;
        
        if (!$tsfe instanceof TypoScriptFrontendController) {
            return;
        }

        $virtualPage = $virtualRouteData['virtualPage'];
        $templatePage = $virtualRouteData['templatePage'];
        $flightRoute = $virtualRouteData['flightRoute'];

        // Replace TSFE page data with virtual page data
        $tsfe->page = $virtualPage;
        $tsfe->id = $virtualPage['uid'];

        // Store virtual route data globally for content rendering
        $GLOBALS['TYPO3_CONF_VARS']['USER']['virtualRouteData'] = $virtualRouteData;

        // Override content rendering to use template page content
        $this->setupVirtualContentRendering($tsfe, $templatePage, $flightRoute);
    }

    /**
     * Setup virtual content rendering using template page content
     */
    protected function setupVirtualContentRendering(
        TypoScriptFrontendController $tsfe, 
        array $templatePage, 
        array $flightRoute
    ): void {
        // Load template page content elements
        $templateContent = $this->loadTemplatePageContent($templatePage['uid']);
        
        // Process placeholders in template content
        $processedContent = $this->processContentPlaceholders($templateContent, $flightRoute);
        
        // Store processed content for rendering
        $GLOBALS['TYPO3_CONF_VARS']['USER']['virtualTemplateContent'] = $processedContent;

        // Override lib.dynamicContent in TypoScript to use our virtual content
        if (isset($tsfe->tmpl->setup['lib.'])) {
            $tsfe->tmpl->setup['lib.']['dynamicContent'] = 'USER';
            $tsfe->tmpl->setup['lib.']['dynamicContent.'] = [
                'userFunc' => 'Bgs\\FlightLandingPages\\UserFunc\\VirtualContentRenderer->render'
            ];
        }
    }

    /**
     * Load content elements from template page
     */
    protected function loadTemplatePageContent(int $templatePageUid): array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tt_content');

        $result = $queryBuilder
            ->select('*')
            ->from('tt_content')
            ->where(
                $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($templatePageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', 0),
                $queryBuilder->expr()->eq('hidden', 0)
            )
            ->orderBy('sorting')
            ->executeQuery();

        return $result->fetchAllAssociative();
    }

    /**
     * Process placeholders in content elements
     */
    protected function processContentPlaceholders(array $contentElements, array $flightRoute): array
    {
        $placeholders = [
            '[_origin_code_]' => $flightRoute['origin_code'] ?? '',
            '[_origin_name_]' => $flightRoute['origin_name'] ?? '',
            '[_origin_type_]' => $flightRoute['origin_type'] ?? '',
            '[_destination_code_]' => $flightRoute['destination_code'] ?? '',
            '[_destination_name_]' => $flightRoute['destination_name'] ?? '',
            '[_destination_type_]' => $flightRoute['destination_type'] ?? '',
            '[_route_slug_]' => $flightRoute['route_slug'] ?? '',
            '[_route_]' => ($flightRoute['origin_name'] ?? '') . ' → ' . ($flightRoute['destination_name'] ?? ''),
            '[_route_dash_]' => ($flightRoute['origin_code'] ?? '') . '-' . ($flightRoute['destination_code'] ?? ''),
            '[_route_text_]' => ($flightRoute['origin_name'] ?? '') . ' to ' . ($flightRoute['destination_name'] ?? ''),
        ];

        foreach ($contentElements as &$contentElement) {
            // Process text fields
            $textFields = ['header', 'subheader', 'bodytext', 'header_link'];
            
            foreach ($textFields as $field) {
                if (isset($contentElement[$field]) && is_string($contentElement[$field])) {
                    $contentElement[$field] = str_replace(
                        array_keys($placeholders), 
                        array_values($placeholders), 
                        $contentElement[$field]
                    );
                }
            }
        }

        return $contentElements;
    }
}
